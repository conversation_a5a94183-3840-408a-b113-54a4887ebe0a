import React, { useRef, useEffect } from 'react';

interface Star {
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    opacity: number;
    pulseSpeed: number;
    pulsePhase: number;
    colorIndex: number;
    depth: number;
}

const ConstellationBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        let animationId: number;
        let stars: Star[] = [];
        let nebulaClouds: any[] = []; // Nuages de nébuleuse

        // ========== RÉGLAGES CONSTELLATIONS ==========
        const maxDistance = 150; // LIGNE 28 - Distance max connexions étoiles (80-200)
        const starCount = 600; // LIGNE 29 - Nombre total d'étoiles (300-600)
        let mouseX = 0;
        let mouseY = 0;
        let mouseInfluence = 130; // LIGNE 32 - Rayon influence souris (100-200)
        let mouseConnections = true; // LIGNE 33 - Activer/désactiver connexions souris

        // ========== RÉGLAGES NÉBULEUSE ==========
        const nebulaCloudCount = 8; // LIGNE 39 - Nombre de nuages cosmiques (4-15)
        const nebulaOpacity = 0.08; // LIGNE 40 - Opacité générale nébuleuse (0.03-0.15)
        const nebulaMovementSpeed = 0.0001; // LIGNE 41 - Vitesse dérive nuages (0.00005-0.0005)
        let nebulaEnabled = true; // LIGNE 42 - Activer/désactiver nébuleuse

        // ========== RÉGLAGES ROTATION CÉLESTE ==========
        let celestialRotationSpeed = 0.000005; // LIGNE 36 - Vitesse rotation ULTRA-LENTE (0.000001-0.0005)
        // Plus petit = plus lent | Plus grand = plus rapide
        let rotationSmoothness = 0.99; // LIGNE 38 - Fluidité rotation (0.95-0.99)
        // Plus proche de 1 = plus fluide | ATTENTION: Ne jamais dépasser 1.0 !
        let centerX = 0;
        let centerY = 0;

        // Couleurs réalistes des étoiles basées sur leur température
        const starColors = [
            '#FFFFFF', // Blanc pur - étoiles très chaudes (majorité)
            '#F8F8FF', // Blanc fantôme - étoiles chaudes
            '#E6E6FA', // Lavande très pâle - étoiles blanches
            '#87CEEB', // Bleu ciel - étoiles très chaudes (type O/B)
            '#B0E0E6', // Bleu poudre - étoiles chaudes
            '#FFE4B5', // Blanc cassé/crème - étoiles comme le Soleil
            '#FFEFD5', // Blanc papaye - étoiles légèrement plus froides
            '#FFB347', // Orange pêche - étoiles plus froides (type K)
            '#FF6347', // Rouge tomate - étoiles froides (type M)
            '#CD853F'  // Brun doré - étoiles très froides
        ];

        // ========== COULEURS NÉBULEUSE COSMIQUE ==========
        const nebulaColors = [
            '#4B0082', // Indigo profond - hydrogène ionisé
            '#8A2BE2', // Violet bleu - régions d'émission
            '#FF69B4', // Rose vif - hydrogène alpha
            '#FF1493', // Rose profond - soufre ionisé
            '#00CED1', // Turquoise - oxygène doublement ionisé
            '#1E90FF', // Bleu dodger - régions de réflexion
            '#FF4500', // Orange rouge - poussière chaude
            '#DA70D6'  // Orchidée - mélange gazeux
        ];

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            // Centre de rotation (légèrement décalé pour un effet plus naturel)
            centerX = canvas.width * 0.6; // Décalé vers la droite
            centerY = canvas.height * 0.3; // Décalé vers le haut

            stars = [];
            nebulaClouds = [];

            // ========== GÉNÉRATION NUAGES NÉBULEUSE ==========
            for (let i = 0; i < nebulaCloudCount; i++) {
                nebulaClouds.push({
                    x: Math.random() * canvas.width * 1.5 - canvas.width * 0.25, // Déborde légèrement
                    y: Math.random() * canvas.height * 1.5 - canvas.height * 0.25,
                    size: Math.random() * 400 + 200, // Taille nuage (200-600px)
                    colorIndex: Math.floor(Math.random() * nebulaColors.length),
                    opacity: Math.random() * nebulaOpacity + nebulaOpacity * 0.5,
                    driftX: (Math.random() - 0.5) * nebulaMovementSpeed,
                    driftY: (Math.random() - 0.5) * nebulaMovementSpeed,
                    pulseSpeed: Math.random() * 0.001 + 0.0005, // Pulsation très lente
                    pulsePhase: Math.random() * Math.PI * 2
                });
            }

            // Créer des couches d'étoiles pour l'effet de profondeur
            for (let i = 0; i < starCount; i++) {
                // Distribution plus large sur tout l'écran avec zones de densité variable
                let x, y;
                const zoneType = Math.random();

                if (zoneType < 0.7) {
                    // 70% des étoiles réparties uniformément
                    x = Math.random() * canvas.width;
                    y = Math.random() * canvas.height;
                } else {
                    // 30% des étoiles dans des zones plus denses (simulation de la Voie Lactée)
                    x = Math.random() * canvas.width;
                    y = Math.random() * canvas.height * 0.6 + canvas.height * 0.2; // Zone centrale
                }

                // ========== RÉGLAGES 4 COUCHES D'ÉTOILES ==========
                const depthLayer = Math.random();
                let size, opacity, pulseSpeed, colorIndex, depth;

                if (depthLayer < 0.4) {
                    // ===== COUCHE 1: ARRIÈRE-PLAN (40%) =====
                    // LIGNES 83-87 - Étoiles très lointaines
                    size = Math.random() * 0.8 + 0.2; // LIGNE 84 - Taille (0.1-1.2)
                    opacity = Math.random() * 0.3 + 0.1; // LIGNE 85 - Opacité (0.05-0.5)
                    pulseSpeed = Math.random() * 0.002 + 0.0005; // LIGNE 86 - Scintillement (0.0001-0.005)
                    colorIndex = Math.floor(Math.random() * 3); // LIGNE 87 - Couleurs (0-9)
                    depth = 0.3; // LIGNE 88 - Profondeur (0.1-0.5)
                } else if (depthLayer < 0.7) {
                    // ===== COUCHE 2: INTERMÉDIAIRE (30%) =====
                    // LIGNES 90-94 - Étoiles moyennes
                    size = Math.random() * 1.5 + 0.5; // LIGNE 91 - Taille (0.3-2.5)
                    opacity = Math.random() * 0.5 + 0.3; // LIGNE 92 - Opacité (0.2-0.8)
                    pulseSpeed = Math.random() * 0.004 + 0.001; // LIGNE 93 - Scintillement (0.0005-0.008)
                    colorIndex = Math.floor(Math.random() * 6); // LIGNE 94 - Couleurs (0-9)
                    depth = 0.6; // LIGNE 95 - Profondeur (0.4-0.8)
                } else if (depthLayer < 0.9) {
                    // ===== COUCHE 3: AVANT-PLAN (20%) =====
                    // LIGNES 97-101 - Étoiles proches
                    size = Math.random() * 2.5 + 1.0; // LIGNE 98 - Taille (0.8-4.0)
                    opacity = Math.random() * 0.7 + 0.4; // LIGNE 99 - Opacité (0.3-1.0)
                    pulseSpeed = Math.random() * 0.006 + 0.002; // LIGNE 100 - Scintillement (0.001-0.01)
                    colorIndex = Math.floor(Math.random() * starColors.length); // LIGNE 101 - Toutes couleurs
                    depth = 0.8; // LIGNE 102 - Profondeur (0.6-1.0)
                } else {
                    // ===== COUCHE 4: PREMIER PLAN (10%) =====
                    // LIGNES 104-108 - Étoiles très brillantes
                    size = Math.random() * 3.5 + 2.0; // LIGNE 105 - Taille (1.5-6.0)
                    opacity = Math.random() * 0.9 + 0.6; // LIGNE 106 - Opacité (0.5-1.0)
                    pulseSpeed = Math.random() * 0.008 + 0.003; // LIGNE 107 - Scintillement (0.002-0.015)
                    colorIndex = Math.floor(Math.random() * starColors.length); // LIGNE 108 - Toutes couleurs
                    depth = 1.0; // LIGNE 109 - Profondeur maximale
                }

                stars.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 0.06 * depth, // Vitesse proportionnelle à la profondeur
                    vy: (Math.random() - 0.5) * 0.06 * depth,
                    size: size,
                    opacity: opacity,
                    pulseSpeed: pulseSpeed,
                    pulsePhase: Math.random() * Math.PI * 2,
                    colorIndex: colorIndex,
                    depth: depth // Ajouter la profondeur pour les effets
                });
            }
        };

        // ========== FONCTION ROTATION CÉLESTE ==========
        const applyCelestialRotation = (star: Star, time: number) => {
            // Calculer la position relative au centre de rotation
            const dx = star.x - centerX;
            const dy = star.y - centerY;

            // LIGNE 95 - Angle de rotation avec fluidité améliorée
            const smoothTime = time * rotationSmoothness; // Application de la fluidité
            const rotationAngle = smoothTime * celestialRotationSpeed;

            // Appliquer la rotation matricielle
            const cos = Math.cos(rotationAngle);
            const sin = Math.sin(rotationAngle);

            const rotatedX = dx * cos - dy * sin;
            const rotatedY = dx * sin + dy * cos;

            // Retourner les nouvelles coordonnées
            return {
                x: rotatedX + centerX,
                y: rotatedY + centerY
            };
        };

        // ========== FONCTION RENDU NÉBULEUSE ==========
        const drawNebulaCloud = (cloud: any, time: number) => {
            if (!nebulaEnabled) return;

            // Pulsation subtile du nuage
            const pulse = Math.sin(time * cloud.pulseSpeed + cloud.pulsePhase) * 0.3 + 0.7;
            const currentOpacity = cloud.opacity * pulse;

            const color = nebulaColors[cloud.colorIndex];

            ctx.save();
            ctx.globalAlpha = currentOpacity;

            // Créer un gradient radial pour le nuage cosmique
            const gradient = ctx.createRadialGradient(
                cloud.x, cloud.y, 0,
                cloud.x, cloud.y, cloud.size
            );

            // Gradient multi-couches pour effet nébuleuse réaliste
            gradient.addColorStop(0, color + '00'); // Centre transparent
            gradient.addColorStop(0.2, color + '20'); // Début visible
            gradient.addColorStop(0.4, color + '40'); // Plus dense
            gradient.addColorStop(0.6, color + '30'); // Diminution
            gradient.addColorStop(0.8, color + '15'); // Très subtil
            gradient.addColorStop(1, color + '00'); // Bord transparent

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(cloud.x, cloud.y, cloud.size, 0, Math.PI * 2);
            ctx.fill();

            // Ajouter un effet de blur pour plus de réalisme
            ctx.globalAlpha = currentOpacity * 0.5;
            ctx.shadowColor = color;
            ctx.shadowBlur = cloud.size * 0.3;
            ctx.beginPath();
            ctx.arc(cloud.x, cloud.y, cloud.size * 0.7, 0, Math.PI * 2);
            ctx.fill();
            ctx.shadowBlur = 0;

            ctx.restore();
        };

        const drawStar = (star: Star, time: number) => {
            // Appliquer la rotation céleste
            const rotatedPos = applyCelestialRotation(star, time);

            // Scintillement naturel multi-couches (comme les vraies étoiles)
            const twinkle1 = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.12 + 0.88;
            const twinkle2 = Math.sin(time * star.pulseSpeed * 1.7 + star.pulsePhase * 0.3) * 0.08 + 0.92;
            const twinkle3 = Math.sin(time * star.pulseSpeed * 0.6 + star.pulsePhase * 1.4) * 0.05 + 0.95;
            const currentOpacity = star.opacity * twinkle1 * twinkle2 * twinkle3 * star.depth;

            // Utiliser la couleur assignée à l'étoile
            const color = starColors[star.colorIndex] || starColors[0];

            ctx.save();

            // ========== RENDU 4 COUCHES VISUELLES ==========

            // ===== COUCHE VISUELLE 1: HALO EXTERNE (ATMOSPHÈRE) =====
            if (star.size > 0.8) {
                ctx.globalAlpha = currentOpacity * 0.15 * star.depth; // LIGNE 179 - Opacité halo externe (0.05-0.3)
                ctx.shadowColor = color;
                ctx.shadowBlur = star.size * 8 * star.depth; // LIGNE 181 - Blur halo externe (4-15)

                const haloGradient = ctx.createRadialGradient(
                    rotatedPos.x, rotatedPos.y, 0,
                    rotatedPos.x, rotatedPos.y, star.size * 6 * star.depth // LIGNE 185 - Rayon halo externe (3-12)
                );
                haloGradient.addColorStop(0, color + '40'); // LIGNE 187 - Transparence centre (20-80)
                haloGradient.addColorStop(0.5, color + '20'); // LIGNE 188 - Transparence milieu (10-40)
                haloGradient.addColorStop(1, color + '00'); // LIGNE 189 - Transparence bord (toujours 00)

                ctx.fillStyle = haloGradient;
                ctx.beginPath();
                ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 6 * star.depth, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }

            // ===== COUCHE VISUELLE 2: HALO MOYEN (LUEUR PRINCIPALE) =====
            ctx.globalAlpha = currentOpacity * 0.4; // LIGNE 197 - Opacité halo moyen (0.2-0.8)
            const mediumHaloGradient = ctx.createRadialGradient(
                rotatedPos.x, rotatedPos.y, 0,
                rotatedPos.x, rotatedPos.y, star.size * 3.5 // LIGNE 200 - Rayon halo moyen (2-6)
            );
            mediumHaloGradient.addColorStop(0, color + '80'); // LIGNE 202 - Transparence centre (60-FF)
            mediumHaloGradient.addColorStop(0.4, color + '40'); // LIGNE 203 - Transparence milieu (20-80)
            mediumHaloGradient.addColorStop(1, color + '00'); // LIGNE 204 - Transparence bord (toujours 00)

            ctx.fillStyle = mediumHaloGradient;
            ctx.beginPath();
            ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 3.5, 0, Math.PI * 2);
            ctx.fill();

            // ===== COUCHE VISUELLE 3: CORPS ÉTOILE (BLUR SUBTIL) =====
            ctx.globalAlpha = currentOpacity; // LIGNE 211 - Opacité corps étoile (pleine)
            ctx.shadowColor = color;
            ctx.shadowBlur = star.size * 1.5; // LIGNE 213 - Blur corps étoile (0.5-3)

            const coreGradient = ctx.createRadialGradient(
                rotatedPos.x, rotatedPos.y, 0,
                rotatedPos.x, rotatedPos.y, star.size * 1.2 // LIGNE 217 - Rayon corps étoile (0.8-2)
            );
            coreGradient.addColorStop(0, color); // LIGNE 219 - Couleur centre (pleine)
            coreGradient.addColorStop(0.6, color + 'CC'); // LIGNE 220 - Couleur milieu (80% opacité)
            coreGradient.addColorStop(1, color + '80'); // LIGNE 221 - Couleur bord (50% opacité)

            ctx.fillStyle = coreGradient;
            ctx.beginPath();
            ctx.arc(rotatedPos.x, rotatedPos.y, star.size, 0, Math.PI * 2);
            ctx.fill();

            // ===== COUCHE VISUELLE 4: POINT CENTRAL BRILLANT =====
            if (star.size > 1.5) { // LIGNE 228 - Seuil taille pour point central (1-3)
                ctx.globalAlpha = currentOpacity * 0.9; // LIGNE 229 - Opacité point central (0.7-1.0)
                ctx.shadowBlur = star.size * 0.5; // LIGNE 230 - Blur point central (0.2-1)
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 0.3, 0, Math.PI * 2); // LIGNE 233 - Rayon point central (0.1-0.5)
                ctx.fill();
            }

            ctx.shadowBlur = 0;
            ctx.restore();

            // Retourner la position rotée pour les connexions
            return rotatedPos;
        };

        const drawConnection = (star1: Star, star2: Star, pos1: {x: number, y: number}, pos2: {x: number, y: number}, distance: number) => {
            const opacity = Math.max(0, (maxDistance - distance) / maxDistance) * 0.08; // Connexions très subtiles

            // Utiliser les couleurs des étoiles connectées
            const color1 = starColors[star1.colorIndex] || starColors[0];
            const color2 = starColors[star2.colorIndex] || starColors[0];

            ctx.save();
            ctx.globalAlpha = opacity;

            // Gradient linéaire pour la connexion
            const gradient = ctx.createLinearGradient(pos1.x, pos1.y, pos2.x, pos2.y);

            gradient.addColorStop(0, color1);
            gradient.addColorStop(1, color2);

            ctx.strokeStyle = gradient;
            ctx.lineWidth = 0.8; // Lignes plus fines
            ctx.beginPath();
            ctx.moveTo(pos1.x, pos1.y);
            ctx.lineTo(pos2.x, pos2.y);
            ctx.stroke();
            ctx.restore();
        };

        const drawMouseConnections = (time: number) => {
            // Dessiner les connexions entre la souris et les étoiles proches
            if (mouseX >= 0 && mouseY >= 0) {
                stars.forEach(star => {
                    const rotatedPos = applyCelestialRotation(star, time);
                    const dx = mouseX - rotatedPos.x;
                    const dy = mouseY - rotatedPos.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < mouseInfluence) {
                        const opacity = Math.max(0, (mouseInfluence - distance) / mouseInfluence) * 0.6;

                        ctx.save();
                        ctx.globalAlpha = opacity;

                        // Gradient vers la souris
                        const gradient = ctx.createLinearGradient(rotatedPos.x, rotatedPos.y, mouseX, mouseY);
                        const color = starColors[star.colorIndex] || starColors[0];

                        gradient.addColorStop(0, color);
                        gradient.addColorStop(1, '#FFFFFF'); // Blanc pour la souris (plus naturel)

                        ctx.strokeStyle = gradient;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(rotatedPos.x, rotatedPos.y);
                        ctx.lineTo(mouseX, mouseY);
                        ctx.stroke();
                        ctx.restore();
                    }
                });

                // Dessiner un point lumineux subtil à la position de la souris
                ctx.save();
                ctx.globalAlpha = 0.4;
                const mouseGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, 12);
                mouseGradient.addColorStop(0, '#FFFFFF');
                mouseGradient.addColorStop(0.3, '#F8F8FF');
                mouseGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

                ctx.fillStyle = mouseGradient;
                ctx.beginPath();
                ctx.arc(mouseX, mouseY, 12, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        };

        const animate = (time: number) => {
            // Fond noir complet pour éviter l'effet d'effacement
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Mettre à jour les positions des étoiles avec influence de la souris (sur les positions de base)
            stars.forEach(star => {
                // Calculer la position actuelle avec rotation céleste pour l'influence de la souris
                const rotatedPos = applyCelestialRotation(star, time);
                const dx = mouseX - rotatedPos.x;
                const dy = mouseY - rotatedPos.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < mouseInfluence && distance > 0) {
                    const force = (mouseInfluence - distance) / mouseInfluence;
                    // Appliquer la force sur les positions de base (pas les positions rotées)
                    star.vx += (dx / distance) * force * 0.005; // Force très douce pour préserver la rotation naturelle
                    star.vy += (dy / distance) * force * 0.005;
                }

                star.x += star.vx;
                star.y += star.vy;

                // Friction pour ralentir progressivement
                star.vx *= 0.99; // Friction plus forte pour maintenir la rotation céleste
                star.vy *= 0.99;

                // Rebond sur les bords (sur les positions de base)
                if (star.x < 0 || star.x > canvas.width) star.vx *= -1;
                if (star.y < 0 || star.y > canvas.height) star.vy *= -1;

                // Garder les étoiles dans les limites
                star.x = Math.max(0, Math.min(canvas.width, star.x));
                star.y = Math.max(0, Math.min(canvas.height, star.y));
            });

            // Calculer les positions rotées pour les connexions
            const rotatedPositions = stars.map(star => applyCelestialRotation(star, time));

            // Dessiner les connexions entre étoiles (avec positions rotées)
            for (let i = 0; i < stars.length; i++) {
                for (let j = i + 1; j < stars.length; j++) {
                    const pos1 = rotatedPositions[i];
                    const pos2 = rotatedPositions[j];
                    const dx = pos1.x - pos2.x;
                    const dy = pos1.y - pos2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < maxDistance) {
                        drawConnection(stars[i], stars[j], pos1, pos2, distance);
                    }
                }
            }

            // Dessiner les connexions vers la souris
            if (mouseConnections) {
                drawMouseConnections(time);
            }

            // Dessiner les étoiles (avec rotation céleste)
            stars.forEach(star => drawStar(star, time));

            animationId = requestAnimationFrame(animate);
        };

        initialize();
        animate(0);

        const handleResize = () => {
            initialize();
        };

        const handleMouseMove = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        const handleMouseLeave = () => {
            mouseX = -1; // Valeur négative pour indiquer que la souris n'est pas sur le canvas
            mouseY = -1;
        };

        const handleMouseEnter = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        window.addEventListener('resize', handleResize);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseleave', handleMouseLeave);
        canvas.addEventListener('mouseenter', handleMouseEnter);

        // Ajouter aussi des listeners sur le document pour une meilleure capture
        document.addEventListener('mousemove', handleMouseMove);

        return () => {
            cancelAnimationFrame(animationId);
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('mousemove', handleMouseMove);
            canvas.removeEventListener('mouseleave', handleMouseLeave);
            canvas.removeEventListener('mouseenter', handleMouseEnter);
            document.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                    pointerEvents: 'auto',
                    zIndex: 1
                }}
            />
        </div>
    );
};

export default ConstellationBackground;
