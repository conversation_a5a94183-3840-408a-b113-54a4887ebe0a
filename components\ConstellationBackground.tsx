import React, { useRef, useEffect, useCallback, useMemo } from 'react';

interface Star {
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    opacity: number;
    pulseSpeed: number;
    pulsePhase: number;
    colorIndex: number;
    depth: number;
    // Cache pour optimisation
    rotatedX?: number;
    rotatedY?: number;
}

interface NebulaCloud {
    x: number;
    y: number;
    size: number;
    colorIndex: number;
    opacity: number;
    driftX: number;
    driftY: number;
    pulseSpeed: number;
    pulsePhase: number;
}

const ConstellationBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const animationIdRef = useRef<number>();
    const lastFrameTimeRef = useRef<number>(0);
    const fpsLimitRef = useRef<number>(60); // Limiter à 60 FPS max

    // Cache des gradients pour éviter les recréations
    const gradientCacheRef = useRef<Map<string, CanvasGradient>>(new Map());

    // Optimisation: Réduire drastiquement les paramètres pour la performance
    const config = useMemo(() => ({
        maxDistance: 120, // Réduit de 150 à 120
        starCount: 300, // Réduit de 600 à 300 (50% de réduction)
        mouseInfluence: 100, // Réduit de 130 à 100
        mouseConnections: true,
        nebulaCloudCount: 4, // Réduit de 8 à 4 (50% de réduction)
        nebulaOpacity: 0.06, // Réduit légèrement
        nebulaMovementSpeed: 0.00005, // Réduit de moitié
        nebulaEnabled: true,
        celestialRotationSpeed: 0.000003, // Réduit légèrement
        rotationSmoothness: 0.98, // Légèrement réduit pour moins de calculs
    }), []);

    let centerX = 0;
    let centerY = 0;

    // Optimisation: Couleurs pré-calculées et réduites pour la performance
    const starColors = useMemo(() => [
        '#FFFFFF', // Blanc pur - étoiles très chaudes (majorité)
        '#F8F8FF', // Blanc fantôme - étoiles chaudes
        '#87CEEB', // Bleu ciel - étoiles très chaudes (type O/B)
        '#FFE4B5', // Blanc cassé/crème - étoiles comme le Soleil
        '#FF6347', // Rouge tomate - étoiles froides (type M)
    ], []);

    // Optimisation: Couleurs nébuleuse réduites
    const nebulaColors = useMemo(() => [
        '#4B0082', // Indigo profond
        '#8A2BE2', // Violet bleu
        '#FF69B4', // Rose vif
        '#00CED1', // Turquoise
    ], []);

    // Cache des calculs trigonométriques
    const trigCacheRef = useRef<Map<string, { cos: number; sin: number }>>(new Map());

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d', {
            alpha: false, // Pas de transparence = plus rapide
            desynchronized: true // Optimisation pour les animations
        });
        if (!ctx) return;

        let stars: Star[] = [];
        let nebulaClouds: NebulaCloud[] = [];
        let mouseX = 0;
        let mouseY = 0;

        // Optimisation: Fonction d'initialisation avec cache
        const initialize = useCallback(() => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            // Centre de rotation optimisé
            centerX = canvas.width * 0.6;
            centerY = canvas.height * 0.3;

            stars = [];
            nebulaClouds = [];
            gradientCacheRef.current.clear(); // Nettoyer le cache

            // Génération optimisée des nuages de nébuleuse
            for (let i = 0; i < config.nebulaCloudCount; i++) {
                nebulaClouds.push({
                    x: Math.random() * canvas.width * 1.2 - canvas.width * 0.1,
                    y: Math.random() * canvas.height * 1.2 - canvas.height * 0.1,
                    size: Math.random() * 300 + 150, // Réduit de 400+200 à 300+150
                    colorIndex: Math.floor(Math.random() * nebulaColors.length),
                    opacity: Math.random() * config.nebulaOpacity + config.nebulaOpacity * 0.5,
                    driftX: (Math.random() - 0.5) * config.nebulaMovementSpeed,
                    driftY: (Math.random() - 0.5) * config.nebulaMovementSpeed,
                    pulseSpeed: Math.random() * 0.0008 + 0.0003, // Réduit
                    pulsePhase: Math.random() * Math.PI * 2
                });
            }

            // Génération optimisée des étoiles avec moins de couches
            for (let i = 0; i < config.starCount; i++) {
                // Distribution simplifiée
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;

                // Optimisation: Seulement 2 couches au lieu de 4
                const depthLayer = Math.random();
                let size, opacity, pulseSpeed, colorIndex, depth;

                if (depthLayer < 0.6) {
                    // Couche arrière-plan (60%)
                    size = Math.random() * 1.2 + 0.3;
                    opacity = Math.random() * 0.4 + 0.2;
                    pulseSpeed = Math.random() * 0.003 + 0.001;
                    colorIndex = Math.floor(Math.random() * 3); // Moins de couleurs
                    depth = 0.5;
                } else {
                    // Couche avant-plan (40%)
                    size = Math.random() * 2.0 + 1.0;
                    opacity = Math.random() * 0.6 + 0.4;
                    pulseSpeed = Math.random() * 0.005 + 0.002;
                    colorIndex = Math.floor(Math.random() * starColors.length);
                    depth = 1.0;
                }

                stars.push({
                    x,
                    y,
                    vx: (Math.random() - 0.5) * 0.04 * depth, // Réduit la vitesse
                    vy: (Math.random() - 0.5) * 0.04 * depth,
                    size,
                    opacity,
                    pulseSpeed,
                    pulsePhase: Math.random() * Math.PI * 2,
                    colorIndex,
                    depth
                });
            }
        }, [config, starColors, nebulaColors]);

        // Fonction de rotation optimisée avec cache
        const applyCelestialRotation = useCallback((star: Star, time: number) => {
            // Calculer la position relative au centre de rotation
            const dx = star.x - centerX;
            const dy = star.y - centerY;

            // Optimisation: Angle de rotation simplifié
            const rotationAngle = time * config.celestialRotationSpeed * config.rotationSmoothness;

            // Cache des calculs trigonométriques
            const cacheKey = Math.floor(rotationAngle * 1000).toString();
            let trigValues = trigCacheRef.current.get(cacheKey);

            if (!trigValues) {
                trigValues = {
                    cos: Math.cos(rotationAngle),
                    sin: Math.sin(rotationAngle)
                };
                // Limiter la taille du cache
                if (trigCacheRef.current.size > 100) {
                    trigCacheRef.current.clear();
                }
                trigCacheRef.current.set(cacheKey, trigValues);
            }

            const rotatedX = dx * trigValues.cos - dy * trigValues.sin;
            const rotatedY = dx * trigValues.sin + dy * trigValues.cos;

            return {
                x: rotatedX + centerX,
                y: rotatedY + centerY
            };
        }, [config]);

        // Fonction de rendu nébuleuse optimisée
        const drawNebulaCloud = useCallback((cloud: NebulaCloud, time: number) => {
            if (!config.nebulaEnabled) return;

            // Optimisation: Pulsation simplifiée
            const pulse = Math.sin(time * cloud.pulseSpeed + cloud.pulsePhase) * 0.2 + 0.8;
            const currentOpacity = cloud.opacity * pulse;

            const color = nebulaColors[cloud.colorIndex];

            // Cache des gradients pour éviter les recréations
            const gradientKey = `nebula_${cloud.colorIndex}_${Math.floor(cloud.size)}`;
            let gradient = gradientCacheRef.current.get(gradientKey);

            if (!gradient) {
                gradient = ctx.createRadialGradient(
                    cloud.x, cloud.y, 0,
                    cloud.x, cloud.y, cloud.size
                );
                gradient.addColorStop(0, color + '00');
                gradient.addColorStop(0.3, color + '30');
                gradient.addColorStop(0.7, color + '20');
                gradient.addColorStop(1, color + '00');

                gradientCacheRef.current.set(gradientKey, gradient);
            }

            ctx.save();
            ctx.globalAlpha = currentOpacity;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(cloud.x, cloud.y, cloud.size, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        }, [config.nebulaEnabled, nebulaColors]);

        // Fonction de rendu d'étoile ultra-optimisée
        const drawStar = useCallback((star: Star, time: number) => {
            // Appliquer la rotation céleste
            const rotatedPos = applyCelestialRotation(star, time);

            // Optimisation: Scintillement simplifié (1 seule couche au lieu de 3)
            const twinkle = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.15 + 0.85;
            const currentOpacity = star.opacity * twinkle * star.depth;

            const color = starColors[star.colorIndex] || starColors[0];

            ctx.save();
            ctx.globalAlpha = currentOpacity;

            // Optimisation: Une seule couche de rendu au lieu de 4
            if (star.size > 1.0) {
                // Halo pour les grandes étoiles seulement
                const gradientKey = `star_${star.colorIndex}_${Math.floor(star.size * 10)}`;
                let gradient = gradientCacheRef.current.get(gradientKey);

                if (!gradient) {
                    gradient = ctx.createRadialGradient(
                        rotatedPos.x, rotatedPos.y, 0,
                        rotatedPos.x, rotatedPos.y, star.size * 2
                    );
                    gradient.addColorStop(0, color);
                    gradient.addColorStop(0.5, color + '80');
                    gradient.addColorStop(1, color + '00');

                    gradientCacheRef.current.set(gradientKey, gradient);
                }

                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 2, 0, Math.PI * 2);
                ctx.fill();
            }

            // Corps principal de l'étoile
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(rotatedPos.x, rotatedPos.y, star.size, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
            return rotatedPos;
        }, [starColors, applyCelestialRotation]);

        // Fonction de connexion optimisée
        const drawConnection = useCallback((star1: Star, star2: Star, pos1: {x: number, y: number}, pos2: {x: number, y: number}, distance: number) => {
            const opacity = Math.max(0, (config.maxDistance - distance) / config.maxDistance) * 0.05; // Réduit l'opacité

            if (opacity < 0.01) return; // Skip les connexions trop faibles

            const color1 = starColors[star1.colorIndex] || starColors[0];

            ctx.save();
            ctx.globalAlpha = opacity;
            ctx.strokeStyle = color1; // Une seule couleur au lieu d'un gradient
            ctx.lineWidth = 0.5; // Lignes encore plus fines
            ctx.beginPath();
            ctx.moveTo(pos1.x, pos1.y);
            ctx.lineTo(pos2.x, pos2.y);
            ctx.stroke();
            ctx.restore();
        }, [config.maxDistance, starColors]);

        // Fonction de connexions souris optimisée
        const drawMouseConnections = useCallback((time: number) => {
            if (mouseX < 0 || mouseY < 0 || !config.mouseConnections) return;

            // Optimisation: Limiter le nombre d'étoiles vérifiées
            const nearbyStars = stars.filter(star => {
                const rotatedPos = applyCelestialRotation(star, time);
                const dx = mouseX - rotatedPos.x;
                const dy = mouseY - rotatedPos.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                return distance < config.mouseInfluence;
            }).slice(0, 10); // Limiter à 10 connexions max

            nearbyStars.forEach(star => {
                const rotatedPos = applyCelestialRotation(star, time);
                const dx = mouseX - rotatedPos.x;
                const dy = mouseY - rotatedPos.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                const opacity = Math.max(0, (config.mouseInfluence - distance) / config.mouseInfluence) * 0.4;

                ctx.save();
                ctx.globalAlpha = opacity;
                ctx.strokeStyle = starColors[star.colorIndex] || starColors[0];
                ctx.lineWidth = 1.5;
                ctx.beginPath();
                ctx.moveTo(rotatedPos.x, rotatedPos.y);
                ctx.lineTo(mouseX, mouseY);
                ctx.stroke();
                ctx.restore();
            });

            // Point souris simplifié
            if (nearbyStars.length > 0) {
                ctx.save();
                ctx.globalAlpha = 0.3;
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.arc(mouseX, mouseY, 8, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }, [config.mouseConnections, config.mouseInfluence, starColors, applyCelestialRotation]);

        // Boucle d'animation ultra-optimisée avec limitation FPS
        const animate = useCallback((time: number) => {
            // Limitation FPS pour économiser les ressources
            const deltaTime = time - lastFrameTimeRef.current;
            const targetFrameTime = 1000 / fpsLimitRef.current;

            if (deltaTime < targetFrameTime) {
                animationIdRef.current = requestAnimationFrame(animate);
                return;
            }

            lastFrameTimeRef.current = time;

            // Fond noir optimisé
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Nébuleuse optimisée
            if (config.nebulaEnabled) {
                nebulaClouds.forEach(cloud => {
                    cloud.x += cloud.driftX;
                    cloud.y += cloud.driftY;

                    // Rebouclage simplifié
                    if (cloud.x < -cloud.size) cloud.x = canvas.width + cloud.size;
                    if (cloud.x > canvas.width + cloud.size) cloud.x = -cloud.size;
                    if (cloud.y < -cloud.size) cloud.y = canvas.height + cloud.size;
                    if (cloud.y > canvas.height + cloud.size) cloud.y = -cloud.size;

                    drawNebulaCloud(cloud, time);
                });
            }

            // Mise à jour des étoiles optimisée
            stars.forEach(star => {
                if (mouseX >= 0 && mouseY >= 0) {
                    const rotatedPos = applyCelestialRotation(star, time);
                    const dx = mouseX - rotatedPos.x;
                    const dy = mouseY - rotatedPos.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < config.mouseInfluence && distance > 0) {
                        const force = (config.mouseInfluence - distance) / config.mouseInfluence;
                        star.vx += (dx / distance) * force * 0.003; // Force réduite
                        star.vy += (dy / distance) * force * 0.003;
                    }
                }

                star.x += star.vx;
                star.y += star.vy;
                star.vx *= 0.98; // Friction légèrement plus forte
                star.vy *= 0.98;

                // Rebonds simplifiés
                if (star.x < 0 || star.x > canvas.width) star.vx *= -1;
                if (star.y < 0 || star.y > canvas.height) star.vy *= -1;
                star.x = Math.max(0, Math.min(canvas.width, star.x));
                star.y = Math.max(0, Math.min(canvas.height, star.y));
            });

            // Optimisation: Calculer les positions rotées une seule fois
            const rotatedPositions = stars.map(star => applyCelestialRotation(star, time));

            // Connexions optimisées - Réduire drastiquement le nombre de vérifications
            const connectionStep = 3; // Vérifier seulement 1 étoile sur 3
            for (let i = 0; i < stars.length; i += connectionStep) {
                for (let j = i + connectionStep; j < stars.length; j += connectionStep) {
                    const pos1 = rotatedPositions[i];
                    const pos2 = rotatedPositions[j];
                    const dx = pos1.x - pos2.x;
                    const dy = pos1.y - pos2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < config.maxDistance) {
                        drawConnection(stars[i], stars[j], pos1, pos2, distance);
                    }
                }
            }

            // Connexions souris
            drawMouseConnections(time);

            // Rendu des étoiles
            stars.forEach(star => drawStar(star, time));

            animationIdRef.current = requestAnimationFrame(animate);
        }, [config, drawNebulaCloud, drawConnection, drawMouseConnections, drawStar, applyCelestialRotation]);

        // Gestionnaires d'événements optimisés
        const handleResize = useCallback(() => {
            initialize();
        }, [initialize]);

        const handleMouseMove = useCallback((event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        }, []);

        const handleMouseLeave = useCallback(() => {
            mouseX = -1;
            mouseY = -1;
        }, []);

        const handleMouseEnter = useCallback((event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        }, []);

        // Initialisation et démarrage
        initialize();
        animate(0);

        // Event listeners
        window.addEventListener('resize', handleResize);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseleave', handleMouseLeave);
        canvas.addEventListener('mouseenter', handleMouseEnter);

        return () => {
            if (animationIdRef.current) {
                cancelAnimationFrame(animationIdRef.current);
            }
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('mousemove', handleMouseMove);
            canvas.removeEventListener('mouseleave', handleMouseLeave);
            canvas.removeEventListener('mouseenter', handleMouseEnter);
        };
    }, [config, starColors, nebulaColors]);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                    pointerEvents: 'auto',
                    zIndex: 1
                }}
            />
        </div>
    );
};

export default ConstellationBackground;
